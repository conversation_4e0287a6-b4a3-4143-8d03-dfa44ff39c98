import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('=== 实验室数据查询API开始 ===');
  
  try {
    // 1. 检查环境变量
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    console.log('环境变量检查:', {
      hasUrl: !!supabaseUrl,
      hasKey: !!anonKey,
      url: supabaseUrl,
      keyLength: anonKey?.length
    });

    if (!supabaseUrl || !anonKey) {
      console.error('环境变量缺失');
      return NextResponse.json({ 
        success: false,
        error: 'Environment variables missing',
        hasUrl: !!supabaseUrl,
        hasKey: !!anonKey
      }, { status: 500 });
    }

    // 2. 获取查询参数
    const { searchParams } = new URL(request.url);
    const sampleType = searchParams.get('sampleType'); // 样品类型
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = searchParams.get('limit') || '50';

    console.log('查询参数:', { sampleType, startDate, endDate, limit });

    // 3. 根据样品类型确定表名
    const tableMapping: Record<string, string> = {
      'shift_samples': '生产日报-FDX',
      'filter_samples': '压滤样化验记录',
      'incoming_samples': '进厂原矿-FDX',
      'outgoing_sample': '出厂精矿-FDX'
    };

    const tableName = tableMapping[sampleType || ''];
    if (!tableName) {
      return NextResponse.json({
        success: false,
        error: 'Invalid sample type',
        validTypes: Object.keys(tableMapping)
      }, { status: 400 });
    }

    // 4. 构建查询URL
    let queryUrl = `${supabaseUrl}/rest/v1/${encodeURIComponent(tableName)}?select=*&order=created_at.desc&limit=${limit}`;
    
    // 添加日期过滤条件
    if (startDate && endDate) {
      // 根据不同表的日期字段名进行过滤
      const dateField = sampleType === 'outgoing_sample' ? '出厂日期' : '日期';
      queryUrl += `&${encodeURIComponent(dateField)}=gte.${startDate}&${encodeURIComponent(dateField)}=lte.${endDate}`;
    }

    console.log('查询URL:', queryUrl);

    // 5. 发送请求到Supabase
    const supabaseResponse = await fetch(queryUrl, {
      method: 'GET',
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    console.log('Supabase响应状态:', supabaseResponse.status, supabaseResponse.statusText);

    // 6. 处理响应
    const responseText = await supabaseResponse.text();
    console.log('Supabase响应内容长度:', responseText.length);

    if (!supabaseResponse.ok) {
      console.error('Supabase查询失败');
      return NextResponse.json({
        success: false,
        error: 'Supabase query failed',
        status: supabaseResponse.status,
        statusText: supabaseResponse.statusText,
        responseText: responseText.substring(0, 500) // 只返回前500字符避免过长
      }, { status: supabaseResponse.status });
    }

    // 7. 解析数据
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error('JSON解析失败:', parseError);
      return NextResponse.json({
        success: false,
        error: 'Failed to parse response',
        responseText: responseText.substring(0, 500)
      }, { status: 500 });
    }

    console.log('查询成功，返回数据条数:', Array.isArray(data) ? data.length : 'N/A');

    return NextResponse.json({
      success: true,
      data: data,
      count: Array.isArray(data) ? data.length : 0,
      tableName: tableName,
      queryParams: { sampleType, startDate, endDate, limit }
    });

  } catch (error) {
    console.error('实验室数据查询API异常:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 5) : undefined
    }, { status: 500 });
  }
}
