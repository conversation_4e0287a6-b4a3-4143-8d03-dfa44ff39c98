# 身份验证和路由重定向测试指南

## 🎯 功能概述

本系统实现了完整的身份验证和智能路由重定向功能：

1. **简化登录验证**：支持工号、用户名等任意格式账号登录（无需邮箱格式）
2. **用户资料查询**：获取用户的工作页面信息
3. **智能重定向**：根据用户工作页面自动跳转到对应页面
4. **默认重定向**：未配置工作页面的用户重定向到demo页面

## 🔧 技术实现

### 数据库表结构

#### 用户资料表
- **表名**：`用户资料`
- **关键字段**：
  - `账号`：用户邮箱（登录凭据）
  - `密码`：用户密码
  - `工作页面`：用户对应的工作页面名称
  - `状态`：账号状态（正常/禁用）

#### 工作页面表
- **表名**：`工作页面`
- **关键字段**：
  - `页面名称`：页面显示名称
  - `页面路由`：对应的URL路径

### API路由

#### POST /api/auth/login
**请求体**：
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**成功响应**：
```json
{
  "success": true,
  "message": "登录成功",
  "redirectUrl": "/production",
  "user": {
    "id": 1,
    "账号": "<EMAIL>",
    "姓名": "张三",
    "部门": "生产部",
    "工作页面": "生产管理"
  }
}
```

## 🧪 测试用户数据

### 邮箱格式账号（兼容旧用户）
| 账号 | 密码 | 姓名 | 部门 | 工作页面 | 预期重定向 |
|------|------|------|------|----------|------------|
| <EMAIL> | test123456 | 张三 | 生产部 | 生产管理 | /production |
| <EMAIL> | demo123456 | 李四 | 质检部 | 质量控制 | /quality |
| <EMAIL> | admin123456 | 王五 | 管理部 | 系统管理 | /admin |
| <EMAIL> | newuser123 | 赵六 | 新员工 | (无) | /demo |

### 工号格式账号（推荐使用）
| 账号 | 密码 | 姓名 | 部门 | 工作页面 | 预期重定向 |
|------|------|------|------|----------|------------|
| FDX001 | 123456 | 张工 | 生产部 | 生产管理 | /production |
| FDX002 | 123456 | 李技师 | 质检部 | 质量控制 | /quality |
| FDX003 | 123456 | 王班长 | 设备部 | 设备监控 | /equipment |

### 姓名拼音格式账号
| 账号 | 密码 | 姓名 | 部门 | 工作页面 | 预期重定向 |
|------|------|------|------|----------|------------|
| zhangsan | 123456 | 张三 | 生产部 | 生产管理 | /production |
| lisi | 123456 | 李四 | 质检部 | 质量控制 | /quality |

### 简单用户名格式
| 账号 | 密码 | 姓名 | 部门 | 工作页面 | 预期重定向 |
|------|------|------|------|----------|------------|
| admin | admin123 | 系统管理员 | 信息部 | 系统管理 | /admin |
| guest | guest123 | 访客用户 | 接待部 | 演示页面 | /demo |

## 🔍 测试步骤

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问登录页面
打开浏览器访问：`http://localhost:3000/auth/login`

### 3. 测试登录流程

#### 测试用例1：工号格式登录
- **账号**：FDX001
- **密码**：123456
- **预期结果**：登录成功，重定向到 `/production` 页面

#### 测试用例2：姓名拼音格式登录
- **账号**：zhangsan
- **密码**：123456
- **预期结果**：登录成功，重定向到 `/production` 页面

#### 测试用例3：简单用户名登录
- **账号**：admin
- **密码**：admin123
- **预期结果**：登录成功，重定向到 `/admin` 页面

#### 测试用例4：访客用户登录
- **账号**：guest
- **密码**：guest123
- **预期结果**：登录成功，重定向到 `/demo` 页面

#### 测试用例5：邮箱格式兼容性
- **账号**：<EMAIL>
- **密码**：test123456
- **预期结果**：登录成功，重定向到 `/production` 页面

#### 测试用例6：错误密码
- **账号**：FDX001
- **密码**：wrongpassword
- **预期结果**：显示"账号或密码错误"错误信息

#### 测试用例7：不存在的用户
- **账号**：notexist
- **密码**：anypassword
- **预期结果**：显示"用户不存在或账号已被禁用"错误信息

### 4. API测试（可选）

使用PowerShell测试API：

```powershell
# 测试正常登录
Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"email":"<EMAIL>","password":"test123456"}' | Select-Object -ExpandProperty Content

# 测试错误密码
Invoke-WebRequest -Uri "http://localhost:3000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"email":"<EMAIL>","password":"wrongpassword"}'
```

## ✅ 验证要点

1. **身份验证**：确保只有正确的邮箱密码组合才能登录成功
2. **路由映射**：验证用户工作页面正确映射到对应的URL路径
3. **默认重定向**：确保没有工作页面的用户重定向到demo页面
4. **错误处理**：验证各种错误情况的处理和提示
5. **加载状态**：确认登录过程中的加载指示器正常显示
6. **用户体验**：验证按钮动画和表单验证等交互细节

## 🔒 安全注意事项

⚠️ **当前实现仅用于开发测试**：
- 密码采用明文存储和比较
- 生产环境必须使用密码哈希（bcrypt等）
- 建议集成完整的Supabase Auth系统
- 添加会话管理和JWT令牌验证

## 📝 后续改进建议

1. **密码安全**：实现密码哈希存储
2. **会话管理**：添加用户会话状态管理
3. **权限控制**：基于用户角色的页面访问控制
4. **记住登录**：实现"记住我"功能
5. **多因素认证**：添加短信或邮箱验证
6. **审计日志**：记录登录活动和安全事件
