"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserProfile } from '@/lib/types/auth';

interface UserContextType {
  user: UserProfile | null;
  setUser: (user: UserProfile | null) => void;
  logout: () => void;
  isLoading: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 从localStorage恢复用户信息
  useEffect(() => {
    try {
      const savedUser = localStorage.getItem('lab_user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      }
    } catch (error) {
      console.error('恢复用户信息失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存用户信息到localStorage
  const handleSetUser = (userData: UserProfile | null) => {
    setUser(userData);
    if (userData) {
      try {
        localStorage.setItem('lab_user', JSON.stringify(userData));
      } catch (error) {
        console.error('保存用户信息失败:', error);
      }
    } else {
      localStorage.removeItem('lab_user');
    }
  };

  // 退出登录
  const logout = () => {
    setUser(null);
    localStorage.removeItem('lab_user');
    // 清除其他可能的用户相关数据
    localStorage.removeItem('lab_session');
    // 重定向到登录页面
    window.location.href = '/auth/login';
  };

  const value: UserContextType = {
    user,
    setUser: handleSetUser,
    logout,
    isLoading
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

// 用户信息工具函数
export function getUserDisplayName(user: UserProfile | null): string {
  if (!user) return '用户';
  
  const name = user.姓名 || user.账号;
  const title = user.职称 || '';
  
  return title ? `${name}${title}` : name;
}

export function getTimeGreeting(): string {
  const currentHour = new Date().getHours();
  
  if (currentHour >= 6 && currentHour < 12) {
    return "早上好";
  } else if (currentHour >= 12 && currentHour < 18) {
    return "下午好";
  } else {
    return "晚上好";
  }
}
